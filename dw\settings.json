{
  "workbench.colorTheme": "Atom One Light",
  "git.autofetch": true,
  "fittencode.languagePreference.displayPreference": "zh-cn",
  "fittencode.languagePreference.commentPreference": "zh-cn",
  "window.confirmSaveUntitledWorkspace": false,
  "editor.unicodeHighlight.nonBasicASCII": false,
  "markdown-preview-enhanced.codeBlockTheme": "auto.css",
  "workbench.iconTheme": "vscode-great-icons",
  "workbench.editorAssociations": {
    "*.copilotmd": "vscode.markdown.preview.editor",
    "{git,gitlens}:/**/*.{md}": "default",
    "*.xlsx": "cweijan.officeViewer"
  },
  "workbench.activityBar.orientation": "vertical",
  // 修改默认终端为 Command Prompt
  "terminal.integrated.defaultProfile.windows": "Command Prompt",
  
  // 更新终端配置
  "terminal.integrated.profiles.windows": {
    "Command Prompt": {
      "path": "C:\\Windows\\System32\\cmd.exe",
      "args": ["/k", "D:\\Anaconda\\Scripts\\activate.bat && conda activate python312"]
    }
  },
  "python.terminal.activateEnvironment": true,
  "files.autoSave": "afterDelay",
  "database-client.autoSync": true,
  "redhat.telemetry.enabled": false,
  "evenBetterToml.schema.associations": {},
  "material-icon-theme.rootFolders.associations": {},
  "explorer.confirmDelete": false,
  "errorLens.statusBarColorsEnabled": true,
  "errorLens.codeLensEnabled": true,
  "errorLens.enableOnDiffView": true,
  "errorLens.problemRangeDecorationEnabled": true,
  "hediet.vscode-drawio.resizeImages": null,
  "explorer.confirmDragAndDrop": false,
  "extensions.allowed": {
    "*": true
  },
  "python.terminal.executeInFileDir": true,
  "python.terminal.activateEnvInCurrentTerminal": true,
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Python: Current File",
        "type": "debugpy",
        "request": "launch",
        "stopOnEntry": false,
        "program": "${file}",
        "cwd": "${fileDirname}",
        "console": "integratedTerminal"
    }
    ],
  },
  "code-runner.executorMap": {
    


    "javascript": "node",
    "java": "cd $dir && javac $fileName && java $fileNameWithoutExt",
    "c": "cd $dir && gcc $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "zig": "zig run",
    "cpp": "cd $dir && g++ $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "objective-c": "cd $dir && gcc -framework Cocoa $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "php": "php",
    "python": "& D:/Anaconda/envs/python312/python.exe",
    "perl": "perl",
    "perl6": "perl6",
    "ruby": "ruby",
    "go": "go run",
    "lua": "lua",
    "groovy": "groovy",
    "powershell": "powershell -ExecutionPolicy ByPass -File",
    "bat": "cmd /c",
    "shellscript": "bash",
    "fsharp": "fsi",
    "csharp": "scriptcs",
    "vbscript": "cscript //Nologo",
    "typescript": "ts-node",
    "coffeescript": "coffee",
    "scala": "scala",
    "swift": "swift",
    "julia": "julia",
    "crystal": "crystal",
    "ocaml": "ocaml",
    "r": "Rscript",
    "applescript": "osascript",
    "clojure": "lein exec",
    "haxe": "haxe --cwd $dirWithoutTrailingSlash --run $fileNameWithoutExt",
    "rust": "cd $dir && rustc $fileName && $dir$fileNameWithoutExt",
    "racket": "racket",
    "scheme": "csi -script",
    "ahk": "autohotkey",
    "autoit": "autoit3",
    "dart": "dart",
    "pascal": "cd $dir && fpc $fileName && $dir$fileNameWithoutExt",
    "d": "cd $dir && dmd $fileName && $dir$fileNameWithoutExt",
    "haskell": "runghc",
    "nim": "nim compile --verbosity:0 --hints:off --run",
    "lisp": "sbcl --script",
    "kit": "kitc --run",
    "v": "v run",
    "sass": "sass --style expanded",
    "scss": "scss --style expanded",
    "less": "cd $dir && lessc $fileName $fileNameWithoutExt.css",
    "FortranFreeForm": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "fortran-modern": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "fortran_fixed-form": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "fortran": "cd $dir && gfortran $fileName -o $fileNameWithoutExt && $dir$fileNameWithoutExt",
    "sml": "cd $dir && sml $fileName",
    "mojo": "mojo run",
    "erlang": "escript",
    "spwn": "spwn build",
    "pkl": "cd $dir && pkl eval -f yaml $fileName -o $fileNameWithoutExt.yaml",
    "gleam": "gleam run -m $fileNameWithoutExt"
  },
  "css.lint.unknownProperties": "error",
  "[python]": {
    
    "diffEditor.ignoreTrimWhitespace": false,
    "editor.defaultColorDecorators": "never",
    "editor.formatOnType": true,
    "editor.wordBasedSuggestions": "off"
  },
  "github.copilot.enable": {
    "*": false
  },
  "git.openRepositoryInParentFolders": "always",
  "augment.chat.userGuidelines": "",
  "python.languageServer": "Pylance",
  "python.analysis.supportAllPythonDocuments": true,
  "http.proxy": "http://127.0.0.1:8800",
  "https.proxy": "https://127.0.0.1:8800",
  "http.experimental.systemCertificatesV2":true,
  "http.useLocalProxyConfiguration":true,
  "http.proxyStrictSSL": false,
  "http.proxySupport":"on",
  "editor.dropIntoEditor.preferences": [

  ],
  "python.linting.enabled": false,
  "EnglishChineseDictionary.enableHover": true,
  "python.condaPath": "D:\\Anaconda\\Scripts\\conda.exe",
  "python.defaultInterpreterPath": "D:/Anaconda/envs/python312/python.exe",
  "augment.completions.enableAutomaticCompletions": true,
}