import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)

import datetime
import logging
import re
import warnings
from pprint import pprint

import idna
import pandas as pd
import pymysql

from dw import settings
from insure.models import InsureArea, InsureAgeSex
from public.models import PublicStatistics, PublicAreaBaseInsure
from transfrom.utils.utils import query_sql
from task.utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class DataVBzHxbInsureV3(CacheManager):
    def __init__(self):
        super().__init__()
        self.DB = settings.DATABASES['default']  # dw数据数据库
        self.product_set_code = 'binzhou_studentV2'  # 产品集编码
        self.city = '滨州'  # 城市
        self.version = '滨州护学保-二期'  # 产品期，用于指标名称标准化
        self.type = 'insure'  # 统计大类
        self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # self.end_datetime = '2025-06-30 23:59:59'
        # 24小时前的时间
        self.start_datetime = (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')
        # self.sale_start_time_zero = '2025-08-15 00:00:00'
        self.sale_start_time_zero = '2025-03-18 00:00:00'
        self.today = datetime.datetime.today().strftime('%Y-%m-%d')
        # 昨天的时间
        self.yesterday = (datetime.datetime.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        # 七日前的时间
        self.seven_days_ago = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
        self.end_time = self.today + ' 00:00:00'
        self.yesterday_time = self.yesterday + ' 00:00:00'
        self.city_name_list = ['滨州市', '北海新区', '博兴县', '市直', '开发区', '惠民县', '无棣县', '沾化区',
                               '滨城区', '邹平市','阳信县','高新区']
        self.age_labels = ["3-6岁", "7-11岁", "12-17岁", "18-25岁"]

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn
    

    def extract_city(self, data_string, type_pattern='人数'):
        """
        匹配城市名称
        :param data_string: 数据字符串
        :param type_pattern: 匹配关键字，默认为'人数'
        """
        match = re.search(rf'-{type_pattern}-([^-]+)-', data_string)
        if match:
            # 如果找到了城市名，返回该城市名
            return match.group(1)
        else:
            # 如果没有找到具体的城市名，则返回"全市"
            if f'-{type_pattern}-' in data_string:
                return '滨州市'
            else:
                # 如果没有 '-人数-' 模式，则不进行提取
                return None
            

    def extract_city_and_version(self, data_string, type_pattern='人数', type_version='当期值'):
        """
        匹配city_name和product_version列
        :param data_string: 数据字符串
        :param type_pattern: 匹配关键字开始，默认为'人数'
        :param type_version: 匹配关键字结尾，默认为'当期值'
        """
        # 定义正则表达式模式
        pattern = rf'-{type_pattern}-(?:([^-]+)-)?([^-]+?)-{type_version}'
        # 遍历DataFrame的每一行

        match = re.search(pattern, data_string)
        if match:
            # 提取城市名和产品版本
            city = match.group(1)  # 城市名可能为None
            if city is None:
                city = '滨州市'
            version = match.group(2)  # 产品版本总是存在
            return {'city_name': city, 'product_version': version}
        else:
            return None

    def df_to_dict(self, df, key_column, value_columns):
        """
        将DataFrame转换为字典

        参数:
        - df: 输入的DataFrame
        - key_column: 用作字典键的列名
        - value_columns: 用作字典值的列名列表

        返回:
        - 一个字典，其中键是key_column指定的列的值，值是由value_columns指定的列组成的字典
        """
        # 创建空字典
        dict_data = {}

        # 迭代DataFrame的每一行
        for _, row in df.iterrows():
            # 获取键
            key = row[key_column]

            # 如果键不在字典中，则添加一个空列表
            if key not in dict_data:
                dict_data[key] = []

            # 添加值
            value = {col: row[col] for col in value_columns}
            dict_data[key].append(value)

        return dict_data

    def get_area_code(self):
        """
        获取区域的编码
        """
        df_area_code = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                ['name', 'code']]
        # code转成datavcode的默认名称
        df_area_code.rename(columns={'code': 'adcode', 'name': 'city_name'}, inplace=True)
        df_area_code.reset_index(drop=True, inplace=True)
        return df_area_code

    def cache_area_code(self):
        """
        从数据库表中获取所有产品信息，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        由于多次从数据库读取数据，访问频率过高，存在访问失败的可能，所以增加缓存
        """
        try:
            df_area_code = self.get_area_code()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache('get_area_code', df_area_code)
            return df_area_code
        except Exception as e:
            logger.error(f'{type(self).__name__} cache_area_code error:{e}')
            raise ValueError(f'{type(self).__name__} cache_area_code error:{e}')

    def get_insure_data(self):
        """
        总参保情况列表(累计参保人数、累计保费、今日参保人数、昨日参保人数、全市(或区域)项目参保率、三款产品参保率（全市或区域）)
        """
        # 获取地区产品编码
        # 累计参保人数、累计保费、今日（昨日）参保人数的指标名称
        # 1、累计参保人数
        with self.get_connection() as conn:
            df_total_person_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='人'), conn)
            # name 含-人数-,避免后续有续保人数，出现问题
            df_total_person_count = df_total_person_count[df_total_person_count['name'].str.contains('-人数')&
                                                          ~df_total_person_count['name'].str.contains('续保人数')&
                                                          df_total_person_count['name'].str.contains('-累计值')]
            df_total_person_count['city_name'] = df_total_person_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='人数'))
            df_total_person_count['value'].fillna(0, inplace=True)
            print(df_total_person_count)

            if df_total_person_count.empty:
                df_total_person_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            else:
                df_total_person_count['value'] = df_total_person_count['value'].astype(int)
                df_total_person_count['value'] = df_total_person_count['value'].fillna(0).astype(int)
            df_total_person_count = df_total_person_count[['city_name', 'value']]
            # 最终以json输出，转成dict
            dict_total_person_count = self.df_to_dict(df_total_person_count, 'city_name', ['value'])

            # 2、累计保费
            df_total_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='元'), conn)
            # name 含-销售额-累计值,避免后续其他情况，出现问题
            df_total_amount = df_total_amount[df_total_amount['name'].str.contains('销售额')&
                df_total_amount['name'].str.contains('-累计值')& ~df_total_amount[
                'name'].str.contains('线下') & ~df_total_amount['name'].str.contains('线上') & ~df_total_amount[
                'name'].str.contains('省本级')]

            df_total_amount['city_name'] = df_total_amount['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销售额'))
            df_total_amount['value'].fillna(0, inplace=True)
            if df_total_amount.empty:
                df_total_amount = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            else:
                df_total_amount['value'] = df_total_amount['value'].fillna(0).astype(float)
                df_total_amount['value'] = round(df_total_amount['value'] / 10000, 2)
            dict_total_amount = self.df_to_dict(df_total_amount, 'city_name', ['value'])
            

            # 3、今日参保人数
            df_today_person_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='人',
                                                                  end_time=self.end_time), conn)
            # name 含-人数-当期值,避免后续其他情况，出现问题
            df_today_person_count = df_today_person_count[df_today_person_count['name'].str.contains('人数')&
                df_today_person_count['name'].str.contains('-当期值')& ~df_today_person_count[
                'name'].str.contains('线下') & ~df_today_person_count['name'].str.contains('线上') & ~df_today_person_count[
                'name'].str.contains('省本级')& ~df_today_person_count['name'].str.contains('小时')]


            df_today_person_count['city_name'] = df_today_person_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='人数'))
            df_today_person_count['value'].fillna(0, inplace=True)

            if df_today_person_count.empty:
                df_today_person_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            else:
                df_today_person_count['value'] = df_today_person_count['value'].fillna(0).astype(int)
            dict_today_person_count = self.df_to_dict(df_today_person_count, 'city_name', ['value'])


            # 4、昨日参保人数
            df_yesterday_person_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='人',
                                                                  end_time=self.yesterday_time), conn)
            # name 含-人数-当期值,避免后续其他情况，出现问题
            df_yesterday_person_count = df_yesterday_person_count[df_yesterday_person_count['name'].str.contains('人数')&
                df_yesterday_person_count['name'].str.contains('-当期值')& ~df_yesterday_person_count[
                'name'].str.contains('线下') & ~df_yesterday_person_count['name'].str.contains('线上') & ~df_yesterday_person_count[
                'name'].str.contains('省本级')& ~df_yesterday_person_count['name'].str.contains('小时')]

            df_yesterday_person_count['city_name'] = df_yesterday_person_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='人数'))
            df_yesterday_person_count['value'].fillna(0, inplace=True)

            if df_yesterday_person_count.empty:
                df_yesterday_person_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            else:
                df_yesterday_person_count['value'] = df_yesterday_person_count['value'].fillna(0).astype(int)
            dict_yesterday_person_count = self.df_to_dict(df_yesterday_person_count, 'city_name', ['value'])


            # 5、全市项目参保率
            df_total_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_total_rate = df_total_rate[
                df_total_rate['name'].str.contains('参保率') & df_total_rate['name'].str.contains('当期值') & 
                ~df_total_rate['name'].str.contains('学版')]
            df_total_rate['city_name'] = df_total_rate['name'].apply(
                lambda x: self.extract_city(x, type_pattern='参保率'))
            df_total_rate['value'].fillna(0, inplace=True)
            df_total_rate.reset_index(drop=True, inplace=True)

            if df_total_rate.empty:
                df_total_rate= pd.DataFrame({'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            else:
                df_total_rate['value'] = df_total_rate['value'].fillna(0).round(1).astype(float)
            dict_total_rate = self.df_to_dict(df_total_rate, 'city_name', ['value'])

            # 6、三款产品销量（全市或区域）
            total_product_person = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                   statistical_type='product_person')
            df_total_product_person = pd.DataFrame(list(total_product_person.values('key', 'value','additional_info')))
            df_total_product_person.rename(columns={'key': 'type','additional_info':'city_name'}, inplace=True)
            df_total_product_person['city_name'].fillna('滨州市', inplace=True)
            df_total_product_person['value'] = df_total_product_person['value'].astype(int)
            df_total_product_person['type'] = df_total_product_person['type'].apply(
                lambda x: '惠学款' if '惠学' in x else x)
            df_total_product_person['type'] = df_total_product_person['type'].apply(
                lambda x: '乐学款' if '乐学' in x else x)
            df_total_product_person['type'] = df_total_product_person['type'].apply(
                lambda x: '优学款' if '优学' in x else x)
            dict_total_product_person = self.df_to_dict(df_total_product_person, 'city_name', ['type','value'])
        return dict_total_person_count, dict_total_amount, dict_today_person_count, dict_yesterday_person_count, dict_total_rate, dict_total_product_person


    def get_school_data(self):
        """
        学校销售人数数据
        """

        school_data =  PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                               statistical_type='school')
        df_school_data = pd.DataFrame(list(school_data.values('key', 'value','additional_info'))).rename(columns={'key': 'y', 'value': 'x','additional_info':'city_name'})
        if df_school_data.empty:
            df_school_data = pd.DataFrame()
        else:
            df_school_data = df_school_data.sort_values(by='x', ascending=False)
            df_school_data.reset_index(drop=True, inplace=True)
            df_school_data['x'] = df_school_data['x'].astype(int)
            # 留前10个学校，学校过多，或导致地图有问题
            df_school_data = df_school_data.head(10)
        dict_school_data = df_school_data.to_dict(orient='records')
        return dict_school_data

    def get_area_data(self):
        """
        区域销售单量数据
        """

        area_data = InsureArea.objects.filter(product_set_code=self.product_set_code)
        df_area_data = pd.DataFrame(list(area_data.values('name', 'total_count', 'insure_ratio')))
        if df_area_data.empty:
            df_area_data = pd.DataFrame()
            df_area_data_count= pd.DataFrame()
        else:
            df_area_data['insure_ratio'].fillna(0, inplace=True)
            df_area_data['total_count'].fillna(0, inplace=True)
            # 删除合计行
            df_area_data = df_area_data[df_area_data['name'] != '合计']
            df_area_data.sort_values(by='insure_ratio', ascending=False, inplace=True)
            df_area_data['insure_ratio'] = df_area_data['insure_ratio'].astype(float)
            df_area_data.reset_index(drop=True, inplace=True)
            df_area_data['position'] = df_area_data.index + 1
            df_area_data.rename(columns={'name': 'y','insure_ratio':'x'}, inplace=True)
            df_area_data['x'] =df_area_data['x']*100
            df_area_data_count = df_area_data.sort_values(by='total_count', ascending=False)
            df_area_data_count = df_area_data_count[['y','total_count']]
            df_area_data_count['total_count'] = df_area_data_count['total_count'].astype(int)
            df_area_data_count.rename(columns={'total_count':'x'}, inplace=True)
        dict_area_data = df_area_data.to_dict(orient='records')
        dict_area_data_count = df_area_data_count.to_dict(orient='records')
        return dict_area_data,dict_area_data_count

    def get_person_data(self):
        """
        获取参保人群数据
        """
        person_data = InsureAgeSex.objects.filter(product_set_code=self.product_set_code)
        df_person_data = pd.DataFrame(list(person_data.values('sex', 'age_distribution', 'value')))
        age_mapping = {age: i for i, age in enumerate(self.age_labels)}
        df_person_data['age_order'] = df_person_data['age_distribution'].map(age_mapping)
        df_person_data = df_person_data.sort_values(by=['age_order'])
        df_person_data = df_person_data.drop('age_order', axis=1).reset_index(drop=True)
        df_person_data.rename(columns={'age_distribution': 'x','sex':'s','value':'y'}, inplace=True)
        dict_person_data = df_person_data.to_dict(orient='records')
        return dict_person_data

    def get_pay_data(self):
        """
        获取缴费数据
        """
        pay_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                   statistical_type='pay_person')
        df_pay_data = pd.DataFrame(list(pay_data.values('key', 'value')))
        if df_pay_data.empty:
            df_pay_data = pd.DataFrame()
        else:
            df_pay_data['value'] = df_pay_data['value'].astype(int)
            df_pay_data.rename(columns={'key': 'type'}, inplace=True)
        dict_pay_data = df_pay_data.to_dict(orient='records')
        return dict_pay_data
    
    def get_last_7_days_data(self):
        """
        最近7日销量数据
        """
        with self.get_connection() as conn:
            start_datetime = max(
                datetime.datetime.strptime(self.seven_days_ago, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S'))
            df_last_7_days_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_7_days_data = df_last_7_days_data[df_last_7_days_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_7_days_data['end_time'] = df_last_7_days_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_last_7_days_data['value'] = df_last_7_days_data['value'].astype(int)
            df_last_7_days_data['month_day'] = df_last_7_days_data['end_time'].apply(lambda x: x[-5:])
            df_last_7_days_data.rename(columns={'month_day': 'x', 'value': 'y'}, inplace=True)
            dict_last_7_days_data = df_last_7_days_data.to_dict(orient='records')
            return dict_last_7_days_data
    
    def get_content(self):
        """
        获取全部数据
        """
        data = {}
        dict_total_person_count, dict_total_amount, dict_today_person_count, dict_yesterday_person_count, dict_total_rate, dict_total_product_person = self.get_insure_data()
        data['累计参保人数'] = dict_total_person_count
        data['累计保费'] = dict_total_amount
        data['今日参保人数'] = dict_today_person_count
        data['昨日参保人数'] = dict_yesterday_person_count
        data['全市项目参保率'] = dict_total_rate
        data['三款产品销售人数'] = dict_total_product_person
        #
        # data['学校销售人数数据'] = self.get_school_data()
        # dict_area_data, dict_area_data_count = self.get_area_data()
        # data['区域参保率'] = dict_area_data
        # data['区域销售人数数据'] = dict_area_data_count
        # data['参保人群数据'] = self.get_person_data()
        # data['支付数据'] = self.get_pay_data()
        # data['近7日投保量'] = self.get_last_7_days_data()
        return data

    def cache_content(self):
        """
        缓存全部数据
        """
        try:
            df_content = self.get_content()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache('get_content', df_content)
            return df_content
        except Exception as e:
            logger.error(f'{type(self).__name__} cache_content error:{e}')
            raise ValueError(f'{type(self).__name__} cache_content error:{e}')

    def get_datav_data(self):
        """
        获取数据
        """
        data = self.get_from_cache('get_content')
        return data


if __name__ == '__main__':
    data = DataVBzHxbInsureV3()
    # product_set_code = data.product_set_code
    # print(data.get_area_code())
    # data.cache_area_code()
    # dict_total_person_count, dict_total_amount, dict_today_person_count, dict_yesterday_person_count, dict_total_rate, dict_total_product_rate = data.get_insure_data()
    # print(dict_today_person_count)
    # data.get_last_24_hour_data()
    # data.get_last_7_days_data()
    # data.get_school_data()
    # data.get_area_data()
    # data.get_person_data()
    # df = data.get_content()
    # pprint(df)
    data.cache_content()
    datav = data.get_datav_data()
    pprint(datav)
